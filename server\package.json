{"name": "server", "version": "1.0.0", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "node app.js", "seed": "node scripts/seedFirestore.js", "test-firebase": "node scripts/testFirebaseConnection.js", "reset-firestore": "node scripts/resetFirestore.js", "check-data": "node scripts/checkFirestoreData.js", "test-flows": "node scripts/testUserFlows.js", "test-api": "node scripts/testAPIEndpoints.js", "setup": "npm run test-firebase && npm run seed", "fresh-start": "npm run reset-firestore -- --confirm && npm run seed"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google-cloud/firestore": "^7.11.3", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "firebase-admin": "^13.4.0", "google-gax": "^5.0.3", "moment": "^2.30.1", "multer": "^2.0.2", "nvm": "^0.0.4", "qs": "^6.14.0", "react-firebase-hooks": "^5.1.1", "react-router-dom": "^7.7.1", "request": "^2.88.2"}, "devDependencies": {"nodemon": "^3.1.10"}}