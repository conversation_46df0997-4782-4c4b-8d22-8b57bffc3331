rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection rules
    match /mm_users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Orders collection rules
    match /orders/{orderId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.userId || 
         request.auth.uid == resource.data.partnerId);
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Services collection rules
    match /services/{serviceId} {
      allow read: if true; // Public read access for services
      allow write: if request.auth != null; // Only authenticated users can write
    }
    
    // Reviews collection rules
    match /reviews/{reviewId} {
      allow read: if true; // Public read access for reviews
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
      allow update, delete: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }
    
    // Partners collection rules
    match /partners/{partnerId} {
      allow read: if true; // Public read access for partner info
      allow write: if request.auth != null && 
        request.auth.uid == partnerId;
    }
  }
}
